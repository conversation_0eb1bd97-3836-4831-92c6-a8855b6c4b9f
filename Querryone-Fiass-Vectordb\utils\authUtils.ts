/**
 * Authentication utilities for managing user sessions and logout functionality
 */

import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

// List of all storage keys that should be cleared on logout
const STORAGE_KEYS_TO_CLEAR = [
  // User session data
  'resultUser',
  'user_email',
  'userEmail',
  // Pinecone configuration
  'pinecone_api_key',
  'pinecone_index_name',
  'pineconeApiKeys',
  'userPineconeIndexes',
  // FAISS configuration
  'faiss_index_name',
  'faiss_embed_model',
  'faiss_client_email',
  'selectedFaissIndex',
  // Application settings
  'use_dev_environment',
  'redirectAfterLogin',
  // Chat and animation state (if needed)
  'animation-state',
  'chat-list',
  'modal-state'
];

/**
 * Performs complete logout process including storage cleanup and redirect
 */
export const performLogout = async (router: AppRouterInstance) => {
  try {
    console.log('🔄 Starting logout process...');
    
    // Step 1: Clear sessionStorage
    try {
      STORAGE_KEYS_TO_CLEAR.forEach((key) => {
        if (typeof window !== 'undefined' && window.sessionStorage) {
          sessionStorage.removeItem(key);
        }
      });
      console.log('✅ SessionStorage cleared');
    } catch (error) {
      console.warn('⚠️ Error clearing sessionStorage:', error);
    }

    // Step 2: Clear localStorage
    try {
      STORAGE_KEYS_TO_CLEAR.forEach((key) => {
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.removeItem(key);
        }
      });
      console.log('✅ LocalStorage cleared');
    } catch (error) {
      console.warn('⚠️ Error clearing localStorage:', error);
    }

    // Step 3: Clear any additional browser storage if needed
    try {
      if (typeof window !== 'undefined' && 'indexedDB' in window) {
        // Clear any IndexedDB data if your app uses it
        // This is optional and depends on your app's storage strategy
      }
    } catch (error) {
      console.warn('⚠️ Error clearing additional storage:', error);
    }

    // Step 4: Clear any cookies if needed
    try {
      if (typeof document !== 'undefined') {
        // Clear authentication cookies if any
        document.cookie.split(";").forEach((c) => {
          const eqPos = c.indexOf("=");
          const name = eqPos > -1 ? c.substr(0, eqPos) : c;
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
        });
      }
    } catch (error) {
      console.warn('⚠️ Error clearing cookies:', error);
    }

    console.log('✅ Logout process completed successfully');

    // Step 5: Redirect to sign-in page
    try {
      await router.replace('/sign-in');
      console.log('✅ Redirected to sign-in page');
    } catch (redirectError) {
      console.error('❌ Error redirecting to sign-in:', redirectError);
      // Fallback redirect
      if (typeof window !== 'undefined') {
        window.location.href = '/sign-in';
      }
    }

  } catch (error) {
    console.error('❌ Error during logout process:', error);
    throw error;
  }
};

/**
 * Checks if user is currently authenticated
 */
export const isAuthenticated = (): boolean => {
  try {
    if (typeof window === 'undefined') return false;
    
    const userData = sessionStorage.getItem('resultUser');
    const userEmail = localStorage.getItem('user_email') || localStorage.getItem('userEmail');
    
    return !!(userData && userEmail);
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
};

/**
 * Gets current user data from session storage
 */
export const getCurrentUser = () => {
  try {
    if (typeof window === 'undefined') return null;
    
    const userData = sessionStorage.getItem('resultUser');
    if (userData) {
      return JSON.parse(userData);
    }
    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Gets current user email from localStorage
 */
export const getCurrentUserEmail = (): string | null => {
  try {
    if (typeof window === 'undefined') return null;
    
    return localStorage.getItem('user_email') || localStorage.getItem('userEmail');
  } catch (error) {
    console.error('Error getting current user email:', error);
    return null;
  }
};

/**
 * Force logout without async operations - for emergency situations
 */
export const forceLogout = () => {
  try {
    // Clear storage synchronously
    if (typeof window !== 'undefined') {
      STORAGE_KEYS_TO_CLEAR.forEach((key) => {
        try {
          sessionStorage.removeItem(key);
          localStorage.removeItem(key);
        } catch (e) {
          // Ignore individual errors
        }
      });
      
      // Force redirect
      window.location.href = '/sign-in';
    }
  } catch (error) {
    console.error('❌ Error in force logout:', error);
  }
};
