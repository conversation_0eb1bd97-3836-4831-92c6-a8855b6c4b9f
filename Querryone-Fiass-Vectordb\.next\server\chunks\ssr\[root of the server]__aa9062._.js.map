{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/pages/_app.tsx"], "sourcesContent": ["import type { AppProps } from 'next/app';\r\nimport '../styles/globals.css';\r\nimport React from 'react';\r\n\r\nfunction MyApp({ Component, pageProps }: AppProps) {\r\n  return (\r\n    <React.StrictMode>\r\n      <Component {...pageProps} />\r\n    </React.StrictMode>\r\n  );\r\n}\r\n\r\nexport default MyApp; "], "names": [], "mappings": ";;;;AAEA;;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,SAAS,EAAY;IAC/C,qBACE,qKAAC,mGAAA,CAAA,UAAK,CAAC,UAAU;kBACf,cAAA,qKAAC;YAAW,GAAG,SAAS;;;;;;;;;;;AAG9B;uCAEe"}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}