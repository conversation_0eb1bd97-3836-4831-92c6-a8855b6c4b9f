'use client';

import React, { useState, useEffect } from 'react';
import FileUploadPage from '@/components/FileUploadPage';
import { useRouter } from 'next/navigation';
import { PiSignOut } from 'react-icons/pi';
import Image from 'next/image';
import logoLight from "@/public/images/logo5.png";
import logoDark from "@/public/images/logo6.png";
import { useTheme } from 'next-themes';
import GradientBackground from '@/components/ui/GradientBackground';
import { useLogout } from '@/hooks/useLogout';

export default function StandaloneFileUploadPage() {
  const router = useRouter();
  const [currentLogo, setCurrentLogo] = useState(logoLight);
  const { resolvedTheme } = useTheme();
  const {
    isLoggingOut,
    showLogoutConfirm,
    handleLogoutClick,
    handleLogout,
    handleCancelLogout,
    error
  } = useLogout();

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);



  return (
    <div className="min-h-screen bg-white dark:bg-n0 text-n500 dark:text-n30 relative">
      <GradientBackground />
      <div className="container mx-auto py-6 px-25  relative z-10">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Image src={currentLogo} alt="QuerryOne Logo" className="mr-1"  />
          </div>

          <div className="flex items-center gap-10">
            <button
              onClick={handleLogoutClick}
              className="flex items-center text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors py-2 px-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <PiSignOut className="mr-2" />
              Sign Out
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-n0 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
          <FileUploadPage />
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
            <h2 className="text-lg font-semibold text-n800 dark:text-n10 mb-4">
              Confirm Logout
            </h2>
            <p className="text-sm text-n600 dark:text-n40 mb-6">
              Are you sure you want to log out? You will need to sign in again to access your account.
            </p>
            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            )}
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelLogout}
                className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors"
                disabled={isLoggingOut}
              >
                Cancel
              </button>
              <button
                onClick={handleLogout}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoggingOut}
              >
                <PiSignOut className="mr-2" />
                {isLoggingOut ? 'Logging out...' : 'Log Out'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
