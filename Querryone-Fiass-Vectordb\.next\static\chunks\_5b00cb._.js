(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_5b00cb._.js", {

"[project]/hooks/useModalOpen.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>useModalOpen)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
"use client";
;
function useModalOpen() {
    _s();
    const [modal, setModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const modalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useModalOpen.useEffect": ()=>{
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "useModalOpen.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["useModalOpen.useEffect"];
        }
    }["useModalOpen.useEffect"], []);
    const handleClickOutside = (event)=>{
        if (modalRef.current && !modalRef.current.contains(event.target)) {
            setModal(false);
        }
    };
    return {
        modal,
        setModal,
        modalRef
    };
}
_s(useModalOpen, "71AK5gFArfbYZ6ene7uxM//KY+4=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/useLogout.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useLogout": (()=>useLogout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/utils/authUtils.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
const useLogout = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isLoggingOut, setIsLoggingOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showLogoutConfirm, setShowLogoutConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const handleLogoutClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLogout.useCallback[handleLogoutClick]": ()=>{
            setShowLogoutConfirm(true);
        }
    }["useLogout.useCallback[handleLogoutClick]"], []);
    const handleCancelLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLogout.useCallback[handleCancelLogout]": ()=>{
            setShowLogoutConfirm(false);
            setError(null);
        }
    }["useLogout.useCallback[handleCancelLogout]"], []);
    const handleLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLogout.useCallback[handleLogout]": async ()=>{
            if (isLoggingOut) return; // Prevent multiple simultaneous logout attempts
            try {
                setIsLoggingOut(true);
                setError(null);
                // Close confirmation modal
                setShowLogoutConfirm(false);
                // Perform logout
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["performLogout"])(router);
            } catch (error) {
                console.error('❌ Error during logout:', error);
                setError(error instanceof Error ? error.message : 'An error occurred during logout');
                // Still try to redirect on error
                try {
                    router.replace('/sign-in');
                } catch (redirectError) {
                    console.error('❌ Failed to redirect after logout error:', redirectError);
                    // Force redirect as last resort
                    if ("TURBOPACK compile-time truthy", 1) {
                        window.location.href = '/sign-in';
                    }
                }
            } finally{
                setIsLoggingOut(false);
            }
        }
    }["useLogout.useCallback[handleLogout]"], [
        router,
        isLoggingOut
    ]);
    return {
        isLoggingOut,
        showLogoutConfirm,
        handleLogoutClick,
        handleLogout,
        handleCancelLogout,
        error
    };
};
_s(useLogout, "53vwozL1igWJeYHUY67g5Jr1oW8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/stores/modal.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useMainModal": (()=>useMainModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
const useMainModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set)=>({
        show: false,
        modalName: "",
        modalOpen: (name)=>set({
                show: true,
                modalName: name
            }),
        modalClose: ()=>set({
                show: false,
                modalName: ""
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/stores/chatList.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useChatHandler": (()=>useChatHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/api/api.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
;
const syncToServer = async (chatList)=>{
    const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
    const userId = resultUser._id?.$oid;
    const username = resultUser.username;
    const mobileno = resultUser.mobileno;
    if (!userId) return;
    try {
        // Step 1: Fetch existing data
        const fetchRes = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseUrl"]}/eSearch?userId=${userId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "xxxid": __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uid"]
            }
        });
        const fetchData = await fetchRes.json();
        // Step 2: Only delete resources that have the `chats` key
        if (Array.isArray(fetchData?.source)) {
            for (const item of fetchData.source){
                try {
                    const parsed = JSON.parse(item);
                    const resourceId = parsed?._id?.$oid;
                    const hasChats = Array.isArray(parsed?.chats);
                    if (resourceId && hasChats) {
                        await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseUrl"]}/eDelete?resourceId=${resourceId}&userId=${userId}`, {
                            method: "DELETE",
                            headers: {
                                "Content-Type": "application/json",
                                "xxxid": __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uid"]
                            }
                        });
                    }
                } catch (err) {
                    console.warn("Skipping invalid or non-chat item:", item);
                }
            }
        }
        // Step 3: Save new chat list with extra user info
        const bodyData = {
            chats: chatList,
            username: username || "",
            mobileno: mobileno || ""
        };
        await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseUrl"]}/eCreate?userId=${userId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "xxxid": __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uid"]
            },
            body: JSON.stringify(bodyData)
        });
    } catch (error) {
        console.error("Failed to sync chats to server:", error);
    }
};
const useChatHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((set, get)=>({
        chatList: [],
        userQuery: "",
        isLoading: false,
        isAnimation: false,
        updateChatList: async ()=>{
            // Only run on client side to prevent hydration errors
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
            const userId = resultUser._id?.$oid;
            if (!userId) return;
            try {
                const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["baseUrl"]}/eSearch?userId=${userId}`, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "xxxid": __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$api$2f$api$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uid"]
                    }
                });
                const data = await response.json();
                const seenChatIds = new Set();
                const chatList = [];
                if (Array.isArray(data?.source)) {
                    for (const item of data.source){
                        try {
                            const parsed = JSON.parse(item);
                            const chats = parsed?.chats;
                            if (Array.isArray(chats)) {
                                for (const chat of chats){
                                    if (!seenChatIds.has(chat.id)) {
                                        seenChatIds.add(chat.id);
                                        // Here is the key fix:
                                        const processedMessages = (chat.messages || []).map((msg)=>{
                                            if (!msg.isUser && msg.text && typeof msg.text === "object" && "ai_response" in msg.text) {
                                                // Preserve the full structured AI response object
                                                return {
                                                    ...msg,
                                                    text: {
                                                        ai_response: msg.text.ai_response,
                                                        sentence_analysis: msg.text.sentence_analysis || [],
                                                        related_questions: msg.text.related_questions || []
                                                    }
                                                };
                                            } else {
                                                // Leave user messages or plain strings unchanged
                                                return msg;
                                            }
                                        });
                                        chatList.push({
                                            id: chat.id,
                                            title: chat.title || "Untitled",
                                            createdAt: chat.createdAt || new Date().toISOString(),
                                            messages: processedMessages,
                                            indexUsed: chat.indexUsed || 'default'
                                        });
                                    }
                                }
                            }
                        } catch (err) {
                            console.warn("Failed to parse one chat source item:", item);
                        }
                    }
                }
                set((state)=>{
                    // Preserve any recently added chats that might not be on the server yet
                    const serverChatIds = new Set(chatList.map((chat)=>chat.id));
                    const recentLocalChats = state.chatList.filter((chat)=>{
                        // Keep chats that are not on the server and were created in the last 5 minutes
                        if (serverChatIds.has(chat.id)) return false;
                        const chatAge = Date.now() - new Date(chat.createdAt).getTime();
                        return chatAge < 5 * 60 * 1000; // 5 minutes
                    });
                    // Merge server chats with recent local chats
                    const mergedChatList = [
                        ...recentLocalChats,
                        ...chatList
                    ];
                    return {
                        ...state,
                        chatList: mergedChatList
                    };
                });
            } catch (err) {
                console.error("Failed to fetch chat data:", err);
            }
        },
        handleSubmit: (userQuery, chatId, aiResponse, options = {})=>{
            const timestamp = new Date().toISOString();
            let messageText;
            let sentenceAnalysis = [];
            let relatedQuestions = [];
            // Get the current index being used
            const currentIndex = options?.index || (("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex') : ("TURBOPACK unreachable", undefined)) || 'default';
            // Store the selected index in localStorage if provided in options
            if (options?.index && "object" !== 'undefined') {
                localStorage.setItem('faiss_index_name', options.index);
                localStorage.setItem('selectedFaissIndex', options.index);
                console.log(`Stored selected index in localStorage: ${options.index}`);
                // If this is just an index selection with no real query, return early without creating a message
                if (userQuery === "text" || !userQuery) {
                    console.log("Skipping message creation for index selection");
                    return;
                }
            }
            if (aiResponse === undefined || aiResponse === null) {
                messageText = "Sorry, there was an issue with the response.";
            } else if (typeof aiResponse === "string") {
                messageText = aiResponse;
            } else if (typeof aiResponse === "object") {
                if ("sentence_analysis" in aiResponse && Array.isArray(aiResponse.sentence_analysis)) {
                    sentenceAnalysis = aiResponse.sentence_analysis;
                }
                if ("related_questions" in aiResponse && Array.isArray(aiResponse.related_questions)) {
                    relatedQuestions = aiResponse.related_questions;
                }
                if ("ai_response" in aiResponse) {
                    const response = aiResponse.ai_response;
                    messageText = typeof response === "string" ? response : JSON.stringify(response);
                } else {
                    try {
                        messageText = JSON.stringify(aiResponse);
                    } catch (e) {
                        messageText = "Error: Could not process the response.";
                    }
                }
            } else {
                messageText = String(aiResponse);
            }
            set((state)=>{
                const existingChatIndex = state.chatList.findIndex((chat)=>chat.id === chatId);
                if (existingChatIndex !== -1) {
                    const updatedChatList = [
                        ...state.chatList
                    ];
                    const messages = updatedChatList[existingChatIndex].messages;
                    const loadingMessageIndex = messages.findIndex((msg)=>!msg.isUser && msg.text === "__LOADING__");
                    if (loadingMessageIndex !== -1) {
                        const loadingMessage = messages[loadingMessageIndex];
                        messages[loadingMessageIndex] = {
                            isUser: false,
                            text: {
                                ai_response: messageText,
                                sentence_analysis: sentenceAnalysis,
                                related_questions: relatedQuestions
                            },
                            timestamp,
                            messageId: loadingMessage.messageId
                        };
                    } else {
                        messages.push({
                            isUser: false,
                            text: {
                                ai_response: messageText,
                                sentence_analysis: sentenceAnalysis,
                                related_questions: relatedQuestions
                            },
                            timestamp
                        });
                    }
                    const newState = {
                        ...state,
                        chatList: updatedChatList
                    };
                    syncToServer(newState.chatList);
                    return newState;
                } else {
                    const chatTitle = userQuery ? userQuery.slice(0, 30) + (userQuery.length > 30 ? "..." : "") : "New Chat";
                    const newChat = {
                        id: chatId,
                        title: chatTitle,
                        createdAt: timestamp,
                        indexUsed: currentIndex,
                        messages: [
                            {
                                isUser: false,
                                text: {
                                    ai_response: messageText,
                                    sentence_analysis: sentenceAnalysis,
                                    related_questions: relatedQuestions
                                },
                                timestamp
                            }
                        ]
                    };
                    const newState = {
                        ...state,
                        chatList: [
                            newChat,
                            ...state.chatList
                        ]
                    };
                    syncToServer(newState.chatList);
                    return newState;
                }
            });
        },
        addMessage: (message, chatId)=>{
            set((state)=>{
                const existingChatIndex = state.chatList.findIndex((chat)=>chat.id === chatId);
                if (existingChatIndex !== -1) {
                    const updatedChatList = [
                        ...state.chatList
                    ];
                    updatedChatList[existingChatIndex].messages.push(message);
                    return {
                        ...state,
                        chatList: updatedChatList
                    };
                } else {
                    // Get the current index for new chats
                    const currentIndex = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex') || 'default' : ("TURBOPACK unreachable", undefined);
                    const newChat = {
                        id: chatId,
                        title: typeof message.text === "string" ? message.text.slice(0, 30) + (message.text.length > 30 ? "..." : "") : "New Chat",
                        createdAt: message.timestamp,
                        indexUsed: currentIndex,
                        messages: [
                            message
                        ]
                    };
                    return {
                        ...state,
                        chatList: [
                            newChat,
                            ...state.chatList
                        ]
                    };
                }
            });
        },
        addChat: (chat)=>{
            set((state)=>{
                // Check if chat already exists to avoid duplicates
                const existingChatIndex = state.chatList.findIndex((existingChat)=>existingChat.id === chat.id);
                let newChatList;
                if (existingChatIndex !== -1) {
                    // Update existing chat
                    newChatList = [
                        ...state.chatList
                    ];
                    newChatList[existingChatIndex] = chat;
                } else {
                    // Add new chat to the beginning
                    newChatList = [
                        chat,
                        ...state.chatList
                    ];
                }
                const newState = {
                    ...state,
                    chatList: newChatList
                };
                // Sync to server asynchronously
                syncToServer(newState.chatList).catch((error)=>{
                    console.error("Failed to sync new chat to server:", error);
                });
                return newState;
            });
        },
        setUserQuery: (query)=>set({
                userQuery: query
            }),
        setIsLoading: (isLoading)=>set({
                isLoading
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/authUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication utilities for managing user sessions and logout functionality
 */ __turbopack_esm__({
    "forceLogout": (()=>forceLogout),
    "getCurrentUser": (()=>getCurrentUser),
    "getCurrentUserEmail": (()=>getCurrentUserEmail),
    "isAuthenticated": (()=>isAuthenticated),
    "performLogout": (()=>performLogout)
});
// List of all storage keys that should be cleared on logout
const STORAGE_KEYS_TO_CLEAR = [
    // User session data
    'resultUser',
    'user_email',
    'userEmail',
    // Pinecone configuration
    'pinecone_api_key',
    'pinecone_index_name',
    'pineconeApiKeys',
    'userPineconeIndexes',
    // FAISS configuration
    'faiss_index_name',
    'faiss_embed_model',
    'faiss_client_email',
    'selectedFaissIndex',
    // Application settings
    'use_dev_environment',
    'redirectAfterLogin',
    // Chat and animation state (if needed)
    'animation-state',
    'chat-list',
    'modal-state'
];
const performLogout = async (router)=>{
    try {
        console.log('🔄 Starting logout process...');
        // Step 1: Clear sessionStorage
        try {
            STORAGE_KEYS_TO_CLEAR.forEach((key)=>{
                if ("object" !== 'undefined' && window.sessionStorage) {
                    sessionStorage.removeItem(key);
                }
            });
            console.log('✅ SessionStorage cleared');
        } catch (error) {
            console.warn('⚠️ Error clearing sessionStorage:', error);
        }
        // Step 2: Clear localStorage
        try {
            STORAGE_KEYS_TO_CLEAR.forEach((key)=>{
                if ("object" !== 'undefined' && window.localStorage) {
                    localStorage.removeItem(key);
                }
            });
            console.log('✅ LocalStorage cleared');
        } catch (error) {
            console.warn('⚠️ Error clearing localStorage:', error);
        }
        // Step 3: Clear any additional browser storage if needed
        try {
            if ("object" !== 'undefined' && 'indexedDB' in window) {
            // Clear any IndexedDB data if your app uses it
            // This is optional and depends on your app's storage strategy
            }
        } catch (error) {
            console.warn('⚠️ Error clearing additional storage:', error);
        }
        // Step 4: Clear any cookies if needed
        try {
            if (typeof document !== 'undefined') {
                // Clear authentication cookies if any
                document.cookie.split(";").forEach((c)=>{
                    const eqPos = c.indexOf("=");
                    const name = eqPos > -1 ? c.substr(0, eqPos) : c;
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                });
            }
        } catch (error) {
            console.warn('⚠️ Error clearing cookies:', error);
        }
        console.log('✅ Logout process completed successfully');
        // Step 5: Redirect to sign-in page
        try {
            await router.replace('/sign-in');
            console.log('✅ Redirected to sign-in page');
        } catch (redirectError) {
            console.error('❌ Error redirecting to sign-in:', redirectError);
            // Fallback redirect
            if ("TURBOPACK compile-time truthy", 1) {
                window.location.href = '/sign-in';
            }
        }
    } catch (error) {
        console.error('❌ Error during logout process:', error);
        throw error;
    }
};
const isAuthenticated = ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const userData = sessionStorage.getItem('resultUser');
        const userEmail = localStorage.getItem('user_email') || localStorage.getItem('userEmail');
        return !!(userData && userEmail);
    } catch (error) {
        console.error('Error checking authentication status:', error);
        return false;
    }
};
const getCurrentUser = ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const userData = sessionStorage.getItem('resultUser');
        if (userData) {
            return JSON.parse(userData);
        }
        return null;
    } catch (error) {
        console.error('Error getting current user:', error);
        return null;
    }
};
const getCurrentUserEmail = ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return localStorage.getItem('user_email') || localStorage.getItem('userEmail');
    } catch (error) {
        console.error('Error getting current user email:', error);
        return null;
    }
};
const forceLogout = ()=>{
    try {
        // Clear storage synchronously
        if ("TURBOPACK compile-time truthy", 1) {
            STORAGE_KEYS_TO_CLEAR.forEach((key)=>{
                try {
                    sessionStorage.removeItem(key);
                    localStorage.removeItem(key);
                } catch (e) {
                // Ignore individual errors
                }
            });
            // Force redirect
            window.location.href = '/sign-in';
        }
    } catch (error) {
        console.error('❌ Error in force logout:', error);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/logo5.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/logo5.e93f6c78.png");}}),
"[project]/public/images/logo5.png.mjs { IMAGE => \"[project]/public/images/logo5.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/logo5.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 223,
    height: 54,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACl4mJwhZ4GHBAoLTAEBAUcBAQFCAQEBTAEBAUcBAQEhABmVo54ajqCgBhMVOAUEBTcFBQY0BgUHMwUFBjAFBAUhnm0KkfiOWmoAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 2
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/logo6.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/logo6.200288d9.png");}}),
"[project]/public/images/logo6.png.mjs { IMAGE => \"[project]/public/images/logo6.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/logo6.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 500,
    height: 113,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/AAIlLzIGV3aAJjlBQTMzMzAsLCwpNjY2My8vLywKCgoJAAEgJSgDY3J6HTY8OzU1NS4vLy8oLy8vKC0tLSYJCQkIlTgLefW61qsAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 2
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-12.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-12.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-12.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-12.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-12.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-14.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-14.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-14.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-14.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-14.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-1.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-1.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-1.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-1.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-1.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-2.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-2.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-2.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-2.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-2.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-3.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-3.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-3.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-3.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-3.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-4.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-4.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-4.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-4.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-4.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-5.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-5.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-5.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-5.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-5.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-6.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-6.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-6.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-6.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-6.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-7.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-7.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-7.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-7.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-7.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-8.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-8.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-8.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-8.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-8.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-9.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-9.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-9.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-9.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-9.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-10.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-10.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-10.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-10.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-10.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-11.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-11.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-11.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-11.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-11.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-13.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-13.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-13.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-13.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-13.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-15.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-15.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-15.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-15.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-15.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-16.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-16.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-16.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-16.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-16.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-17.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-17.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-17.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-17.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-17.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-18.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-18.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-18.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-18.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-18.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-19.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-19.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-19.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-19.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-19.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-20.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-20.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-20.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-20.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-20.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-21.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-21.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-21.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-21.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-21.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-22.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-22.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-22.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-22.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-22.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-23.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-23.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-23.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-23.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-23.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-24.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-24.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-24.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-24.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-24.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/explore-article-icon-25.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/explore-article-icon-25.7ddeabf2.png");}}),
"[project]/public/images/explore-article-icon-25.png.mjs { IMAGE => \"[project]/public/images/explore-article-icon-25.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/explore-article-icon-25.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 80,
    height: 80,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/figma.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/figma.2cc5335f.png");}}),
"[project]/public/images/figma.png.mjs { IMAGE => \"[project]/public/images/figma.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$figma$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/figma.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$figma$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/shopify.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/shopify.dd59074a.png");}}),
"[project]/public/images/shopify.png.mjs { IMAGE => \"[project]/public/images/shopify.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$shopify$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/shopify.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$shopify$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAyUlEQVR42k3PzQsBYRAG8P3XHBU3Lq5uznJSSnIjKSkOlJODpOQrIkVWkiwlRPJRSKu07LL7Pvbdza6ZyzTzO8zDsCybJYSIUEshMojatOiO3pjf8S2/sL50sTp38ZQeBmLo8FEkcIcSShMfUh0X0o0gBFFHGuCFPcpTP4bbHBJNJ7wZB878wQSn+wwVLoDJvoBozQZP0orlkTPB5tpDfR7WQKRqhztuwWDR0gF9RJBuKupjvMujOAoh145hd1npT/7HNKISYsT8AoRH4XdZ+up6AAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/slack.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/slack.2cc5335f.png");}}),
"[project]/public/images/slack.png.mjs { IMAGE => \"[project]/public/images/slack.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$slack$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/slack.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$slack$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/webflow.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/webflow.2cc5335f.png");}}),
"[project]/public/images/webflow.png.mjs { IMAGE => \"[project]/public/images/webflow.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$webflow$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/webflow.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$webflow$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/airtable.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/airtable.2cc5335f.png");}}),
"[project]/public/images/airtable.png.mjs { IMAGE => \"[project]/public/images/airtable.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$airtable$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/airtable.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$airtable$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/monday.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/monday.2cc5335f.png");}}),
"[project]/public/images/monday.png.mjs { IMAGE => \"[project]/public/images/monday.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$monday$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/monday.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$monday$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 40,
    height: 40,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/video-preview-img-2.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/video-preview-img-2.45c03216.png");}}),
"[project]/public/images/video-preview-img-2.png.mjs { IMAGE => \"[project]/public/images/video-preview-img-2.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$video$2d$preview$2d$img$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/video-preview-img-2.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$video$2d$preview$2d$img$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 408,
    height: 399,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAATklEQVR42pXOMQoAMQgEQL8eEkIQFPU1Ue5xdygpU9wUFi4sC88FXAN338nTPqC1xsy9dyIyM0Rca805oV5jjApERFW/CxFRDX5Egt+rXjd5gy/9DpGeAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/generate-photo-1.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/generate-photo-1.46980204.png");}}),
"[project]/public/images/generate-photo-1.png.mjs { IMAGE => \"[project]/public/images/generate-photo-1.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/generate-photo-1.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 320,
    height: 320,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/generate-photo-2.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/generate-photo-2.46980204.png");}}),
"[project]/public/images/generate-photo-2.png.mjs { IMAGE => \"[project]/public/images/generate-photo-2.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/generate-photo-2.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 320,
    height: 320,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/generate-photo-3.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/generate-photo-3.46980204.png");}}),
"[project]/public/images/generate-photo-3.png.mjs { IMAGE => \"[project]/public/images/generate-photo-3.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/generate-photo-3.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 320,
    height: 320,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/generate-photo-4.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/generate-photo-4.46980204.png");}}),
"[project]/public/images/generate-photo-4.png.mjs { IMAGE => \"[project]/public/images/generate-photo-4.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/generate-photo-4.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$generate$2d$photo$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 320,
    height: 320,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/adjust-photo-modal.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/adjust-photo-modal.87de0da8.png");}}),
"[project]/public/images/adjust-photo-modal.png.mjs { IMAGE => \"[project]/public/images/adjust-photo-modal.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$adjust$2d$photo$2d$modal$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/adjust-photo-modal.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$adjust$2d$photo$2d$modal$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 393,
    height: 593,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAALklEQVR42pXMsREAIAxCUfbfICBM4nB66ay8vAqaj/3C71fVaiQlwXYStzsw7B0ZKFRk366MsAAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/logodefault.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/logodefault.7ab433ae.png");}}),
"[project]/public/images/logodefault.png.mjs { IMAGE => \"[project]/public/images/logodefault.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logodefault$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/logodefault.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logodefault$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 58,
    height: 54,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAEBAgEUJTAqN2uRkkWOxM8/hre8IUpiWwUKDQkAAAAAABIpMy04kL3HPqnj+j2l2+48qeD1Oqnd7yJfdm8DBgcEACqAnJg0uOf8MI2vqhQ0QTceVGteMLbh6i2v1NwPKzMpACyryMMqx+36IGBtWwABAQAKGx4TKLLPyyfK6vMVSVNJACGVpJke1/L9Ia6+tRdUWkklcoBzMLrh7iPF3+QONjovAA44OiwZws3HE+f2/BXm9fUc3fL6Kcjt/h/Q6e8RXGFRAAECAgEMNjcrE6CklhHQ1McSwcW2GZCbjxnE0ssPZmpRi6piXKCTQHwAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 7
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/upgrade-header.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/upgrade-header.64ef4576.png");}}),
"[project]/public/images/upgrade-header.png.mjs { IMAGE => \"[project]/public/images/upgrade-header.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$upgrade$2d$header$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/upgrade-header.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$upgrade$2d$header$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 596,
    height: 256,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAIAAAAhqtkfAAAAP0lEQVR42jWKwQnAAAgDXVzEjz7ENcQqOmOl0HuEHAlERFXNzO529/NxBYiImRFRVc3M3UXkFG6/e/2cXmbmC3yzLEQxtacRAAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 3
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/create-new-bot-img.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/create-new-bot-img.478bf52a.png");}}),
"[project]/public/images/create-new-bot-img.png.mjs { IMAGE => \"[project]/public/images/create-new-bot-img.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$create$2d$new$2d$bot$2d$img$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/create-new-bot-img.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$create$2d$new$2d$bot$2d$img$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 64,
    height: 64,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAn0lEQVR42oWPOQ6CUBiEX+A2Np7BwgNRG60N7lsMcV/iHWiw9gi2GnchVLQ/73sJhI5iksnM/JP5lWXZdqVarzlN/9zx7omroXmAhqcgjfbl2l08pAg0POW0/ACht3zKYP2W0fYrw83HhGhVrndL+quXTA+hjHc/g8n+L2h4JsD17BjJ/BSbIA15gBrquCQE4AQYWz4yf1OPpRLQmr2ZAgVLn2v4A+29AAAAAElFTkSuQmCC",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/constants/data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "accentColorItems": (()=>accentColorItems),
    "aiGeneratorBrainstorm": (()=>aiGeneratorBrainstorm),
    "aiGeneratorOptions": (()=>aiGeneratorOptions),
    "botCategory": (()=>botCategory),
    "chatOptions": (()=>chatOptions),
    "countryOptions": (()=>countryOptions),
    "customBotsData": (()=>customBotsData),
    "emailPreferenceOptions": (()=>emailPreferenceOptions),
    "exploreBotTags": (()=>exploreBotTags),
    "explorePageData": (()=>explorePageData),
    "faqData": (()=>faqData),
    "integrationItemsData": (()=>integrationItemsData),
    "languageOptions": (()=>languageOptions),
    "phpCode": (()=>phpCode),
    "responseStyle": (()=>responseStyle),
    "settingsTabItems": (()=>settingsTabItems),
    "supportMenuItems": (()=>supportMenuItems),
    "themeSettingsData": (()=>themeSettingsData),
    "upgradeModalData": (()=>upgradeModalData),
    "upgradePlanDetails": (()=>upgradePlanDetails)
});
//icons
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-1.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-1.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-2.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-2.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-3.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-3.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-4.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-4.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-5.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-5.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-6.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-6.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-7.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-7.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-8.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-8.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-9.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-9.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-10.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-10.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-11.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-11.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-12.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-12.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-13.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-13.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-14.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-14.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-15.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-15.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-16.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-16.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-17.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-17.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-18.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-18.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-19.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-19.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-20.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-20.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-21.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-21.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-22.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-22.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-23.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-23.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-24.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-24.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/explore-article-icon-25.png.mjs { IMAGE => "[project]/public/images/explore-article-icon-25.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
//integration icons
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$figma$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$figma$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/figma.png.mjs { IMAGE => "[project]/public/images/figma.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$shopify$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$shopify$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/shopify.png.mjs { IMAGE => "[project]/public/images/shopify.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$slack$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$slack$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/slack.png.mjs { IMAGE => "[project]/public/images/slack.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$webflow$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$webflow$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/webflow.png.mjs { IMAGE => "[project]/public/images/webflow.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$airtable$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$airtable$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/airtable.png.mjs { IMAGE => "[project]/public/images/airtable.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$monday$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$monday$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/monday.png.mjs { IMAGE => "[project]/public/images/monday.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_import__("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const chatOptions = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Image Generator",
        label: "image",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiImage"],
        color: "77, 107, 254"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Video Generator",
        label: "video",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiVideo"],
        color: "142, 51, 255"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Audio Generator",
        label: "audio",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSpeakerHigh"],
        color: "255, 86, 48"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Photo Editor",
        label: "retouch",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiImageSquare"],
        color: "255, 171, 0"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Education Feedback",
        label: "data-table",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGraduationCap"],
        color: "34, 197, 94"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Get Advice",
        label: "text",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMegaphone"],
        color: "255, 86, 48"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Code Generator",
        label: "code",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCode"],
        color: "34, 197, 94"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Help me write",
        label: "text",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPencilSimpleLine"],
        color: "77, 107, 254"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Summarize text",
        label: "text",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMagicWand"],
        color: "255, 86, 48"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Problem solving",
        label: "code",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiQuestion"],
        color: "142, 51, 255"
    }
];
const aiGeneratorOptions = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Video Generator",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiVideoCameraFill"],
        color: "142, 51, 255"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Photo Generator",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiImageFill"],
        color: "255, 86, 48"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: " Photo Editor",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiImageSquareFill"],
        color: "77, 107, 254"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Code Generator",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCodeFill"],
        color: "34, 197, 94"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Education Feedback",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGraduationCapFill"],
        color: "255, 171, 0"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Audio Generator",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSpeakerHighFill"],
        color: "255, 86, 48"
    }
];
const upgradePlanDetails = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Basic",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPaperPlaneTilt"],
        price: 0,
        features: [
            "Access to AIQuill mini and reasoning",
            "Standard voice mode",
            "Real-time data from the web with search",
            "Limited access to AIQuill"
        ],
        notProvidedFeatures: [
            "Limited access to file uploads, data analysis, and image generation",
            "Use custom AIQuill"
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Standard",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiAirplaneTilt"],
        price: 59,
        features: [
            "Access to AIQuill mini and reasoning",
            "Standard voice mode",
            "Real-time data from the web with search",
            "Limited access to AIQuill",
            "Limited access to file uploads, data analysis, and image generation"
        ],
        notProvidedFeatures: [
            "Use custom AIQuill"
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Premium",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiRocketLaunch"],
        price: 99,
        features: [
            "Access to AIQuill mini and reasoning",
            "Standard voice mode",
            "Real-time data from the web with search",
            "Limited access to AIQuill",
            "Limited access to file uploads, data analysis, and image generation",
            "Use custom AIQuill"
        ],
        notProvidedFeatures: []
    }
];
const faqData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "What is AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "How does AIQuill work?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "Can I create my own AI chatbot?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "Is coding knowledge required to use AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "What industries can use AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "Does AIQuill support multiple languages?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "Can I integrate AIQuill with my website or app?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "Is there a free version of AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "How secure is AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        question: "How do I get started with AIQuill?",
        answer: "Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications."
    }
];
const supportMenuItems = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "FAQs",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiQuestion"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Change Log",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFileText"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Roadmap",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPath"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Contact",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiEnvelopeSimple"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Privacy",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiLockKey"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Terms",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiClipboardText"]
    }
];
const settingsTabItems = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "General",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGear"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Security",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiLock"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Appearance",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPaintBrushHousehold"]
    }
];
const themeSettingsData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "System",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiLaptop"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Light",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSun"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Dark",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMoon"]
    }
];
const accentColorItems = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#4D6BFE"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#8E33FF"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#00B8D9"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#22C55E"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#FFAB00"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#FF5630"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        color: "#0B1323"
    }
];
const customBotsData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        title: "Research Specialist",
        desc: "Helps with academic research, paper analysis, and citation management",
        tag: "Research",
        color: "77, 107, 254"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        title: "Programming Guide",
        desc: "Assists with programming, code reviews, and best practices",
        tag: "Programming",
        color: "142, 51, 255"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        title: "Writing Mentor",
        desc: "Helps improve writing style, grammar, and content structure",
        tag: "Writing",
        color: "0, 184, 217"
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        title: "Information Specialist",
        desc: "Assists with data analysis, visualization, insights and Data analyst management",
        tag: "Research",
        color: "34, 197, 94"
    }
];
const aiGeneratorBrainstorm = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Ultra Pro AI",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiRobot"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "4K+ Upscaling",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiRocketLaunch"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "High Resolution",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiAirplaneTilt"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Medium Precision",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFire"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Low Resolution",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPaperPlaneTilt"]
    }
];
const upgradeModalData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "2.1v Flash",
        desc: "Get everyday help",
        isNew: true
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "2.0v Flash Thinking Experimental",
        desc: "Best for multi-step reasoning",
        isNew: true
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "1.9.1 Thinking Experimental with apps",
        desc: "Reasoning across YouTube, Maps & Search",
        isNew: true
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "1.9 Flash",
        desc: "Previous Model",
        isNew: false
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "1.5 Flash",
        desc: "Start Journey With AI",
        isNew: false
    }
];
const integrationItemsData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Slack",
        desc: "Real-time messaging and notifications.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$slack$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$slack$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Shopify",
        desc: "Manage your e-commerce platform.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$shopify$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$shopify$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Webflow",
        desc: "Manage your Webflow site content.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$webflow$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$webflow$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Airtable",
        desc: "Connect and manage your Airtable bases.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$airtable$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$airtable$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Monday",
        desc: "Manage team workflows and projects.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$monday$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$monday$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        name: "Figma",
        desc: "Access and preview Figma designs.",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$figma$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$figma$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    }
];
const explorePageData = [
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Featured",
        desc: "Curated top picks from this week",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Video Maker by Descript",
                desc: "Turn your ideas into videos with the AI Video Maker by Descript—a powerful text-to-speech video generator and video editor in one.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Mermaid Chart: diagrams and charts",
                desc: "Official AIQuill from the Mermaid team. Generate a Mermaid diagram or chart with text including video generator and video editor in one.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Form And Survey Maker by demo.co",
                desc: "Build and share live surveys with the AI Video Maker by Descript—a powerful text-to-speech video generator and video editor in one.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Tutor Me",
                desc: "Your personal AI tutor by Khan Academy! I'm Khanmigo Lite - here to help you with math, science, and—a powerful text-to-speech video generator.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Trending",
        desc: "Most popular AIQuill by our community",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Innovations Reshaping the Future",
                desc: "Explore cutting-edge AI advancements driving transformation. From automation to deep learning, discover groundbreaking.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "The Rise of AI Technology",
                desc: "Artificial intelligence is evolving rapidly. Stay updated with the latest AI breakthroughs, trends, and emerging tools revolutionizing.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$6$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Stay Updated with AI Trends",
                desc: "Keep up with AI’s fast-paced evolution. Learn about innovations in automation, robotics, and machine learning.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Exploring the Future of AI",
                desc: "AI is changing how we work and live. Discover upcoming trends, research, and advancements that redefine industries.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI-Powered Future: What’s Next?",
                desc: "Predict the future of AI-driven technology. Explore innovations in AI-powered assistants, automation, and predictive analytics.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI’s Latest Breakthroughs",
                desc: "Get the latest AI news and updates. Learn about groundbreaking research, industry shifts, and revolutionary discoveries.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Writing",
        desc: "Enhance your writing with tools for creation, editing, and style refinement",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI-Powered Writing for Creativity",
                desc: "Enhance your writing with AI tools. Generate ideas, improve grammar, and create engaging content effortlessly.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Write Smarter with AI Help",
                desc: "Boost your productivity with AI writing assistants. Get real-time feedback, refine your style, and craft compelling narratives with ease.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Writing: The Future Tool",
                desc: "From blogs to business reports, AI enhances content creation. Explore AI-driven writing tools that help improve clarity and efficiency.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$13$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Transform Your Words with AI",
                desc: "Experience AI-enhanced writing assistance. Generate professional-quality text, refine drafts, and create high-impact content.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$14$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Text Generation Made Easy",
                desc: "Simplify content creation with AI-powered writing. Generate high-quality text, rephrase sentences, and optimize readability.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Boost Writing Skills with AI",
                desc: "Take your writing to the next level. AI helps enhance tone, grammar, and structure, making every piece clear and engaging.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Productivity",
        desc: "Increase your efficiency",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Tools for Maximum Efficiency",
                desc: "Optimize your workflow with AI-powered automation. Save time, eliminate repetitive tasks, and enhance focus with intelligent.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Work Smarter with AI Solutions",
                desc: "AI enhances productivity by automating tedious tasks. From scheduling to data processing, let AI handle the workload.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Increase Productivity AI Assistance",
                desc: "Streamline your daily tasks with AI-powered organization tools. Stay on top of deadlines, manage projects.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Automation for Smarter Workflows",
                desc: "Let AI handle repetitive work. Automate scheduling, email responses, and task management to free up valuable time.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "The Future of Smart Work",
                desc: "AI-driven solutions make productivity effortless. Use machine learning tools to enhance workplace efficiency, manage tasks.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$21$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Revolutionizing Work with AI Tech",
                desc: "Embrace AI-powered tools for a smarter workflow. Automate time-consuming processes and focus on what truly matters in your work.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$22$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Research & Analysis",
        desc: "Find, evaluate, interpret, and visualize information",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI for Smarter Data Analysis",
                desc: "Unlock deep insights using AI-powered research tools. Analyze large datasets, find trends, and make data-driven decisions efficiently.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$23$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Revolutionizing Research AI Tools",
                desc: "AI is transforming how we analyze data. Enhance your research with machine learning models designed for accuracy and efficiency.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$24$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Insights for Smarter Decisions",
                desc: "Process information faster with AI-driven analytics. From business reports to academic research, AI helps uncover valuable insights.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$25$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Transform Data Actionable Insights",
                desc: "AI-powered analytics provide real-time data interpretation. Identify trends, predict outcomes, and optimize strategies using intelligent.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$1$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Deep Learning for Data Analysis",
                desc: "Leverage deep learning to analyze patterns and trends. AI-powered tools make complex research faster, more precise, and highly effective.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Research: Smarter, Faster, Better",
                desc: "Accelerate research with AI automation. From literature reviews to predictive modeling, AI simplifies complex data analysis.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Education",
        desc: "Explore new ideas, revisit existing skills",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI-Powered Learning for Students",
                desc: "AI transforms education by personalizing learning experiences. Get instant feedback, adaptive quizzes, and AI-generated study plans.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$2$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Smart Education with AI Assistance",
                desc: "Enhance learning with AI-powered tools. From automated tutoring to AI-driven assessments, modern education.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$3$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Tutors: The Future of Learning",
                desc: "AI-powered tutors provide personalized lessons, improving knowledge retention and engagement. Learn at your own pace.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$4$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Learning with AI Technology",
                desc: "AI makes learning smarter. Interactive study assistants help students grasp complex topics, while AI-generated summaries study.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Personalized Learning with AI",
                desc: "AI analyzes your progress and adapts to your learning style. Get tailored recommendations, instant feedback.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Smarter Classrooms with AI",
                desc: "AI-powered tools enhance education with interactive learning, automated grading, and real-time student performance tracking.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Lifestyle",
        desc: "Get tips on travel, workouts, style, food, and more",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI for a Smarter Life",
                desc: "AI simplifies everyday life with intelligent automation, from smart home devices to personalized recommendations.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$9$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Personalized AI for Daily Living",
                desc: "Experience AI-powered convenience. From smart assistants to automated routines, AI enhances comfort, productivity.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$10$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Smart Assistants for Effortless Living",
                desc: "Let AI handle your daily tasks. Voice assistants, smart reminders, and AI-powered planning tools help streamline routines with ease.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$11$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI-Driven Lifestyle Enhancements",
                desc: "Upgrade your daily life with AI solutions. Manage tasks, track fitness, and get tailored recommendations for a seamless experience.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$12$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Technology for Smarter Homes",
                desc: "Control your home with AI-powered automation. From smart lighting to voice-controlled devices, technology simplifies life for greater comfort.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$7$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Live Smarter with AI Innovations",
                desc: "AI enhances your lifestyle by managing schedules, automating tasks, and providing personalized suggestions.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$8$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    },
    {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        title: "Programming",
        desc: "Write code, debug, test, and learn",
        articles: [
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI-Assisted Coding for Developers",
                desc: "Accelerate programming with AI-powered suggestions. Get real-time code recommendations, debug efficiently.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$15$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Code Faster with AI Assistance",
                desc: "AI-driven coding assistants help you write cleaner, more efficient code. Automate repetitive tasks and optimize performance with AI.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$16$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI in Software Development",
                desc: "Transform programming with AI automation. From generating code to predictive debugging, AI is revolutionizing software.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$17$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "Smart Coding with AI Tools",
                desc: "Enhance development workflows with AI-powered solutions. Automate repetitive coding tasks, debug errors, and optimize algorithms.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$18$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "The Future of AI Coding",
                desc: "AI is reshaping programming. Explore tools that suggest code, optimize structures, and streamline development processes.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$19$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            },
            {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                title: "AI Debugging & Optimization",
                desc: "Use AI-driven debugging tools to detect errors faster and enhance code performance, making software development smarter.",
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$explore$2d$article$2d$icon$2d$20$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
            }
        ]
    }
];
const phpCode = `<?php
session_start();
$conn = new 
mysqli("localhost", "root", "", "your_database");
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect);
}
if 
($_SERVER["REQUEST"] == "POST") 
{
    $name = $_POST["name"];
    $gmail = $_POST["gmail"];
    $phone = $_POST["phone"];
    $pass =($_POST["pass"], 
    PASSWORD_BCRYPT);
    $stmt = $conn->
    prepare("INSERT INTO users 
    (name, gmail, phone, pass);
    $stmt->
    bind_param("ssss", $name, $gmail, 
    $phone, $password);
    
<body>
    <h2>Register</h2>
    <?php 
    if(isset($_SESSION['error'])) 
    { echo "<p style='color:red;'>"  
    "</p>"; unset($_SESSION['error']); } ?>
   
</body>
</html>
`;
const countryOptions = [
    {
        value: "afganistan",
        label: "Afganistan"
    },
    {
        value: "america",
        label: "United States"
    },
    {
        value: "london",
        label: "United Kingdom"
    },
    {
        value: "china",
        label: "China"
    }
];
const emailPreferenceOptions = [
    {
        value: "I am okay with promo emails",
        label: "I am okay with promo emails"
    },
    {
        value: "I want only update emails",
        label: "I want only update email"
    },
    {
        value: "I want only security emails",
        label: "I want only security emails"
    },
    {
        value: "I want all emails",
        label: "I want all emails"
    }
];
const languageOptions = [
    {
        value: "English",
        label: "English"
    },
    {
        value: "French",
        label: "French"
    },
    {
        value: "Arabic",
        label: "Arabic"
    },
    {
        value: "Japanese",
        label: "Japanese"
    }
];
const exploreBotTags = [
    "Featured",
    "Trending",
    "Writing",
    "Productivity",
    "Research & Analysis",
    "Lifestyle",
    "Education",
    "Programming"
];
const botCategory = [
    {
        value: "Education",
        label: "Education"
    },
    {
        value: "Research",
        label: "Research"
    },
    {
        value: "Video Creation",
        label: "Video Creation"
    }
];
const responseStyle = [
    {
        value: "Response Style 1",
        label: "Response Style 1"
    },
    {
        value: "Response Style 2",
        label: "Response Style 2"
    },
    {
        value: "Response Style 3",
        label: "Response Style 3"
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/sharingService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "sharingService": (()=>sharingService)
});
class SharingService {
    baseUrl;
    constructor(){
        this.baseUrl = ("TURBOPACK compile-time truthy", 1) ? window.location.origin : ("TURBOPACK unreachable", undefined);
    }
    /**
   * Generate a unique share ID
   */ generateShareId() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    /**
   * Calculate expiration date based on settings
   */ calculateExpirationDate(expiresIn) {
        if (expiresIn === 'never') return null;
        const now = new Date();
        switch(expiresIn){
            case '1day':
                return new Date(now.getTime() + 24 * 60 * 60 * 1000);
            case '7days':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            case '30days':
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            default:
                return null;
        }
    }
    /**
   * Create a shareable version of a chat
   */ async createShareableChat(chat, settings) {
        try {
            const shareId = this.generateShareId();
            const expiresAt = this.calculateExpirationDate(settings.expiresIn);
            // Filter messages based on settings
            const messagesToShare = settings.includeMessages ? chat.messages : chat.messages.slice(0, 1); // Only include first message if not sharing all
            const shareableChat = {
                id: shareId,
                shareId,
                title: chat.title,
                messages: messagesToShare,
                createdAt: chat.createdAt,
                sharedAt: new Date().toISOString(),
                expiresAt: expiresAt?.toISOString(),
                isPublic: settings.publicAccess,
                includeMessages: settings.includeMessages,
                viewCount: 0,
                originalChatId: chat.id
            };
            // In a real implementation, you would save this to your backend
            // For now, we'll store it in localStorage as a demo
            const existingShares = this.getStoredShares();
            existingShares[shareId] = shareableChat;
            localStorage.setItem('sharedChats', JSON.stringify(existingShares));
            const shareUrl = `${this.baseUrl}/shared/chat/${shareId}`;
            return {
                shareId,
                shareUrl
            };
        } catch (error) {
            console.error('Failed to create shareable chat:', error);
            throw new Error('Failed to create share link');
        }
    }
    /**
   * Get a shared chat by share ID
   */ async getSharedChat(shareId) {
        try {
            // In a real implementation, this would fetch from your backend
            const storedShares = this.getStoredShares();
            const sharedChat = storedShares[shareId];
            if (!sharedChat) {
                return null;
            }
            // Check if the share has expired
            if (sharedChat.expiresAt && new Date() > new Date(sharedChat.expiresAt)) {
                // Remove expired share
                delete storedShares[shareId];
                localStorage.setItem('sharedChats', JSON.stringify(storedShares));
                return null;
            }
            // Increment view count
            sharedChat.viewCount += 1;
            storedShares[shareId] = sharedChat;
            localStorage.setItem('sharedChats', JSON.stringify(storedShares));
            return sharedChat;
        } catch (error) {
            console.error('Failed to get shared chat:', error);
            return null;
        }
    }
    /**
   * Get all shares for the current user
   */ getUserShares() {
        try {
            const storedShares = this.getStoredShares();
            return Object.values(storedShares).filter((share)=>!this.isExpired(share));
        } catch (error) {
            console.error('Failed to get user shares:', error);
            return [];
        }
    }
    /**
   * Delete a shared chat
   */ async deleteShare(shareId) {
        try {
            const storedShares = this.getStoredShares();
            if (storedShares[shareId]) {
                delete storedShares[shareId];
                localStorage.setItem('sharedChats', JSON.stringify(storedShares));
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to delete share:', error);
            return false;
        }
    }
    /**
   * Check if a share has expired
   */ isExpired(share) {
        return share.expiresAt ? new Date() > new Date(share.expiresAt) : false;
    }
    /**
   * Get stored shares from localStorage
   */ getStoredShares() {
        try {
            const stored = localStorage.getItem('sharedChats');
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('Failed to parse stored shares:', error);
            return {};
        }
    }
    /**
   * Clean up expired shares
   */ cleanupExpiredShares() {
        try {
            const storedShares = this.getStoredShares();
            const validShares = {};
            Object.entries(storedShares).forEach(([shareId, share])=>{
                if (!this.isExpired(share)) {
                    validShares[shareId] = share;
                }
            });
            localStorage.setItem('sharedChats', JSON.stringify(validShares));
        } catch (error) {
            console.error('Failed to cleanup expired shares:', error);
        }
    }
    /**
   * Generate share metadata for social media
   */ generateShareMetadata(chat) {
        const messageCount = chat.messages.length;
        const userMessages = chat.messages.filter((msg)=>msg.isUser).length;
        return {
            title: `AIQuill Conversation: ${chat.title}`,
            description: `Check out this conversation with ${messageCount} messages and ${userMessages} questions on AIQuill.`
        };
    }
}
const sharingService = new SharingService();
const __TURBOPACK__default__export__ = sharingService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/fileUploadService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Format file size to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size (e.g., "2.5 MB")
 */ __turbopack_esm__({
    "cancelUpload": (()=>cancelUpload),
    "checkIndexExists": (()=>checkIndexExists),
    "checkUploadStatus": (()=>checkUploadStatus),
    "createPineCollectionEntry": (()=>createPineCollectionEntry),
    "fetchEmails": (()=>fetchEmails),
    "formatFileSize": (()=>formatFileSize),
    "getCSVData": (()=>getCSVData),
    "getEmbeddingModels": (()=>getEmbeddingModels),
    "getIndexesByEmail": (()=>getIndexesByEmail),
    "isCancellation": (()=>isCancellation),
    "isFileSizeValid": (()=>isFileSizeValid),
    "isFileTypeAllowed": (()=>isFileTypeAllowed),
    "listCSVFiles": (()=>listCSVFiles),
    "uploadCSVToFaiss": (()=>uploadCSVToFaiss),
    "uploadCSVToPinecone": (()=>uploadCSVToPinecone),
    "uploadFile": (()=>uploadFile),
    "uploadMultipleFiles": (()=>uploadMultipleFiles)
});
const formatFileSize = (bytes)=>{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
const uploadCSVToFaiss = async (file, clientEmail, indexName, updateMode, signal, onProgress, embedModel)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            if (file.type !== 'text/csv') {
                reject(new Error('Only CSV files are supported for FAISS upload'));
                return;
            }
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Add client information to form data if provided
            if (clientEmail) {
                formData.append('client', clientEmail);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add update mode to form data if provided
            if (updateMode) {
                formData.append('update_mode', updateMode);
            }
            // Add embedding model to form data if provided
            if (embedModel) {
                formData.append('embed_model', embedModel);
            }
            // Create a new XMLHttpRequest and connect abort signal
            const xhr = new XMLHttpRequest();
            // Handle abort signal for client-side cancellation
            if (signal) {
                signal.onabort = ()=>{
                    xhr.abort();
                    // Instead of rejecting with an error, resolve with a cancellation object
                    // This prevents the error from appearing in the console
                    resolve({
                        success: false,
                        cancelled: true,
                        message: 'Upload cancelled by user'
                    });
                };
            }
            // Configure the request to our backend endpoint
            xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);
            // Add authentication header only (no Content-Type for FormData)
            xhr.setRequestHeader('xxxid', 'FAISS');
            // Track upload progress if callback provided
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'CSV file uploaded successfully to FAISS',
                            indexName: indexName // Use the user-provided index name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading CSV file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadCSVToPinecone = uploadCSVToFaiss;
const uploadFile = async (file, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Create a new XMLHttpRequest
            const xhr = new XMLHttpRequest();
            // Configure the request
            xhr.open('POST', 'http://localhost:5010/api/upload', true);
            // Track upload progress
            xhr.upload.onprogress = (event)=>{
                if (event.lengthComputable && onProgress) {
                    const progress = Math.round(event.loaded / event.total * 100);
                    onProgress(progress);
                }
            };
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'File uploaded successfully',
                            fileName: file.name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadMultipleFiles = async (files, onProgress)=>{
    const uploadPromises = files.map((file)=>{
        return uploadFile(file, (progress)=>{
            if (onProgress) {
                onProgress(file.name, progress);
            }
        });
    });
    return Promise.all(uploadPromises);
};
const isFileTypeAllowed = (fileType, allowedTypes)=>{
    return allowedTypes.includes(fileType);
};
const isFileSizeValid = (fileSize, maxSizeMB)=>{
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return fileSize <= maxSizeBytes;
};
const listCSVFiles = async (clientEmail)=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-csv-files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                client_email: clientEmail
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error listing CSV files:', error);
        throw error;
    }
};
const getCSVData = async (indexName, limit = 100, offset = 0)=>{
    try {
        const response = await fetch('http://localhost:5010/api/get-csv-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                limit,
                offset
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting CSV data:', error);
        throw error;
    }
};
const getEmbeddingModels = async ()=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-embedding-models', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting embedding models:', error);
        // Return fallback data when backend is not available
        return {
            success: true,
            models: {
                "all-MiniLM-L6-v2": {
                    "name": "all-MiniLM-L6-v2",
                    "description": "Sentence Transformers model for semantic similarity",
                    "dimensions": 384
                },
                "all-mpnet-base-v2": {
                    "name": "all-mpnet-base-v2",
                    "description": "High-quality sentence embeddings",
                    "dimensions": 768
                },
                "paraphrase-MiniLM-L6-v2": {
                    "name": "paraphrase-MiniLM-L6-v2",
                    "description": "Paraphrase detection model",
                    "dimensions": 384
                }
            },
            default_model: "all-MiniLM-L6-v2"
        };
    }
};
const isCancellation = (errorOrResponse)=>{
    // Check for our custom cancellation response
    if (errorOrResponse && errorOrResponse.cancelled === true) {
        return true;
    }
    // Check for error message containing cancellation text
    if (errorOrResponse instanceof Error) {
        const errorMessage = errorOrResponse.message.toLowerCase();
        return errorMessage.includes('cancel') || errorMessage.includes('abort') || errorMessage.includes('user interrupt');
    }
    // Check for response with cancellation status
    if (errorOrResponse && errorOrResponse.status === 'cancelled') {
        return true;
    }
    // Check for response with error_type indicating cancellation
    if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {
        return true;
    }
    return false;
};
const fetchEmails = async ()=>{
    try {
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'QUKTYWK'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array and extract emails
            const emails = data.source.map((jsonStr)=>{
                try {
                    const userObj = JSON.parse(jsonStr);
                    return userObj.email || '';
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    return '';
                }
            }).filter(Boolean); // Remove empty strings
            return emails;
        }
        return [];
    } catch (error) {
        console.error('Error fetching emails:', error);
        return [];
    }
};
const createPineCollectionEntry = async (embedModel, indexName, clientEmail)=>{
    try {
        console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            },
            body: JSON.stringify({
                api_key: embedModel,
                index_name: indexName,
                client: clientEmail // Store email as client
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        console.log('PINE collection entry created successfully:', result);
        return result;
    } catch (error) {
        console.error('Error creating PINE collection entry:', error);
        throw error;
    }
};
const getIndexesByEmail = async (clientEmail)=>{
    try {
        console.log(`Fetching indexes for email: ${clientEmail}`);
        const response = await fetch(`https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        console.log('PINE collection response for email:', data);
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array
            const indexes = data.source.map((jsonStr, index)=>{
                try {
                    const indexObj = JSON.parse(jsonStr);
                    return {
                        _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,
                        email: indexObj.client || clientEmail,
                        index_name: indexObj.index_name || 'N/A',
                        embed_model: indexObj.api_key || 'N/A',
                        source: 'PINE',
                        originalData: indexObj
                    };
                } catch (error) {
                    console.error('Error parsing PINE index JSON:', error);
                    return null;
                }
            }).filter((item)=>item !== null);
            return indexes;
        }
        return [];
    } catch (error) {
        console.error('Error fetching indexes by email:', error);
        return [];
    }
};
const checkIndexExists = async (indexName, client, embedModel)=>{
    try {
        const response = await fetch('http://localhost:5010/api/check-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                client: client,
                embed_model: embedModel
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.success) {
            return {
                exists: data.exists,
                embedding_model: data.embedding_model
            };
        }
        return {
            exists: false
        };
    } catch (error) {
        console.error('Error checking if index exists:', error);
        return {
            exists: false
        };
    }
};
const cancelUpload = async (uploadId, abortController)=>{
    try {
        // First, abort the HTTP request if an AbortController is provided
        if (abortController) {
            try {
                abortController.abort();
                console.log('HTTP request aborted');
            } catch (abortError) {
                // Don't log this as an error since it's expected behavior
                console.log('Note: AbortController already used or not applicable');
            // Continue with server-side cancellation even if client-side abort fails
            }
        }
        // Then, send a cancellation request to the server
        console.log(`Sending cancellation request for upload ${uploadId}`);
        const response = await fetch('http://localhost:5010/api/cancel-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        console.log('Cancellation response:', data);
        // Verify cancellation by checking status
        try {
            const statusResponse = await checkUploadStatus(uploadId);
            console.log('Status after cancellation:', statusResponse);
        } catch (statusError) {
            console.error('Error checking status after cancellation:', statusError);
        // Continue even if status check fails
        }
        return data;
    } catch (error) {
        console.error('Error cancelling upload:', error);
        throw error;
    }
};
const checkUploadStatus = async (uploadId, silent = false)=>{
    try {
        const response = await fetch('http://localhost:5010/api/upload-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        // Log cancellation status if detected
        if (data.success && data.cancelled) {
            console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);
        }
        return data;
    } catch (error) {
        if (!silent) {
            console.error('Error checking upload status:', error);
        }
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/mockServer.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Mock server for file upload API
 * This file simulates a backend server for handling file uploads during development
 *
 * In a real application, you would replace this with actual API calls to your backend
 */ /**
 * Simulates uploading a CSV file to Pinecone
 * @param {File} file - The CSV file to upload
 * @returns {Promise} - Promise that resolves with a mock response
 */ __turbopack_esm__({
    "mockDeleteFile": (()=>mockDeleteFile),
    "mockGetUploadedFiles": (()=>mockGetUploadedFiles),
    "mockUploadCSVToPinecone": (()=>mockUploadCSVToPinecone),
    "mockUploadFile": (()=>mockUploadFile),
    "mockUploadMultipleFiles": (()=>mockUploadMultipleFiles)
});
const mockUploadCSVToPinecone = (file)=>{
    return new Promise((resolve, reject)=>{
        // Validate file type
        if (file.type !== 'text/csv') {
            reject(new Error('Only CSV files are supported for Pinecone upload'));
            return;
        }
        // Simulate network delay
        setTimeout(()=>{
            // Create index name from file name
            const indexName = file.name.replace('.csv', '').replace(/\s+/g, '_').toLowerCase();
            // Randomly succeed or fail (95% success rate)
            const shouldSucceed = Math.random() < 0.95;
            if (shouldSucceed) {
                resolve({
                    success: true,
                    indexName: indexName,
                    vectorCount: Math.floor(Math.random() * 1000) + 100,
                    message: `CSV data successfully uploaded to Pinecone index: ${indexName}`
                });
            } else {
                reject(new Error('Server error: Failed to upload CSV to Pinecone'));
            }
        }, 2000); // Simulate a 2 second delay for processing
    });
};
const mockUploadFile = (file)=>{
    return new Promise((resolve, reject)=>{
        // Simulate network delay
        setTimeout(()=>{
            // Randomly succeed or fail (90% success rate)
            const shouldSucceed = Math.random() < 0.9;
            if (shouldSucceed) {
                resolve({
                    success: true,
                    fileId: `file-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                    uploadDate: new Date().toISOString(),
                    url: `https://example.com/files/${file.name}`,
                    message: 'File uploaded successfully'
                });
            } else {
                reject(new Error('Server error: Failed to upload file'));
            }
        }, 1500); // Simulate a 1.5 second delay
    });
};
const mockUploadMultipleFiles = (files)=>{
    const uploadPromises = files.map((file)=>mockUploadFile(file));
    return Promise.all(uploadPromises);
};
const mockGetUploadedFiles = ()=>{
    return new Promise((resolve)=>{
        // Simulate network delay
        setTimeout(()=>{
            resolve({
                success: true,
                files: [
                    {
                        fileId: 'file-1234567890',
                        fileName: 'example-document.pdf',
                        fileSize: 1024 * 1024 * 2.5,
                        fileType: 'application/pdf',
                        uploadDate: '2023-05-15T10:30:00Z',
                        url: 'https://example.com/files/example-document.pdf'
                    },
                    {
                        fileId: 'file-0987654321',
                        fileName: 'sample-image.jpg',
                        fileSize: 1024 * 512,
                        fileType: 'image/jpeg',
                        uploadDate: '2023-05-14T15:45:00Z',
                        url: 'https://example.com/files/sample-image.jpg'
                    }
                ]
            });
        }, 800); // Simulate a 0.8 second delay
    });
};
const mockDeleteFile = (fileId)=>{
    return new Promise((resolve)=>{
        // Simulate network delay
        setTimeout(()=>{
            resolve({
                success: true,
                fileId,
                message: 'File deleted successfully'
            });
        }, 500); // Simulate a 0.5 second delay
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/fileDownloadService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Service for handling file downloads
 */ /**
 * Download a sample CSV file
 * @param {string} fileName - Name of the sample file to download
 * @returns {Promise<boolean>} - Promise that resolves to true if download was successful
 */ __turbopack_esm__({
    "downloadSampleFile": (()=>downloadSampleFile)
});
const downloadSampleFile = async (fileName = 'querry.csv')=>{
    try {
        // First check if the file exists by making a HEAD request
        try {
            // Use the public directory path
            const checkResponse = await fetch(`/${fileName}`, {
                method: 'HEAD'
            });
            if (!checkResponse.ok) {
                console.error(`Sample file ${fileName} not found. Status: ${checkResponse.status}`);
                return false;
            }
        } catch (checkError) {
            console.error('Error checking if sample file exists:', checkError);
            return false;
        }
        // Create a URL to the file in the public directory
        const fileUrl = `/${fileName}`;
        // Create a link element
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        // Append to the document
        document.body.appendChild(link);
        // Trigger the download
        link.click();
        // Clean up
        document.body.removeChild(link);
        return true;
    } catch (error) {
        console.error('Error downloading sample file:', error);
        return false;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(with-layout)/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/Footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/Header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$MainSidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/MainSidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$MainModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/modals/MainModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$GradientBackground$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/ui/GradientBackground.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$ClientOnly$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/ui/ClientOnly.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$HydrationBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/ui/HydrationBoundary.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$chatList$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/stores/chatList.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
;
;
;
;
function Layout({ children }) {
    _s();
    const [showSidebar, setShowSidebar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { updateChatList } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$chatList$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChatHandler"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Layout.useEffect": ()=>{
            // Only run on client side to prevent hydration errors
            if ("TURBOPACK compile-time truthy", 1) {
                updateChatList();
            }
        }
    }["Layout.useEffect"], [
        updateChatList
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$HydrationBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$ClientOnly$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center items-center h-full",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primaryColor"
                    }, void 0, false, {
                        fileName: "[project]/app/(with-layout)/layout.tsx",
                        lineNumber: 28,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/app/(with-layout)/layout.tsx",
                    lineNumber: 27,
                    columnNumber: 11
                }, void 0)
            }, void 0, false, {
                fileName: "[project]/app/(with-layout)/layout.tsx",
                lineNumber: 26,
                columnNumber: 9
            }, void 0),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$GradientBackground$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/app/(with-layout)/layout.tsx",
                        lineNumber: 33,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-start items-start h-full",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$MainSidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                showSidebar: showSidebar,
                                setShowSidebar: setShowSidebar
                            }, void 0, false, {
                                fileName: "[project]/app/(with-layout)/layout.tsx",
                                lineNumber: 35,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 flex flex-col gap-2 sm:gap-3 justify-between items-center h-full pb-2 sm:pb-3 relative z-20 w-full overflow-hidden",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        showSidebar: showSidebar,
                                        setShowSidebar: setShowSidebar
                                    }, void 0, false, {
                                        fileName: "[project]/app/(with-layout)/layout.tsx",
                                        lineNumber: 40,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-full flex-1 overflow-auto flex flex-col",
                                        children: children
                                    }, void 0, false, {
                                        fileName: "[project]/app/(with-layout)/layout.tsx",
                                        lineNumber: 41,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/app/(with-layout)/layout.tsx",
                                        lineNumber: 44,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(with-layout)/layout.tsx",
                                lineNumber: 39,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(with-layout)/layout.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$MainModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/app/(with-layout)/layout.tsx",
                        lineNumber: 49,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(with-layout)/layout.tsx",
                lineNumber: 32,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(with-layout)/layout.tsx",
            lineNumber: 25,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(with-layout)/layout.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_s(Layout, "NSEVgB4Iv7OTzdzq13nxVvMfoAE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$chatList$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChatHandler"]
    ];
});
_c = Layout;
const __TURBOPACK__default__export__ = Layout;
var _c;
__turbopack_refresh__.register(_c, "Layout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(with-layout)/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=_5b00cb._.js.map