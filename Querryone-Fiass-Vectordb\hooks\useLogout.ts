"use client";

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { performLogout } from '@/utils/authUtils';

/**
 * Custom hook for logout functionality
 */
export const useLogout = () => {
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogoutClick = useCallback(() => {
    setShowLogoutConfirm(true);
  }, []);

  const handleCancelLogout = useCallback(() => {
    setShowLogoutConfirm(false);
    setError(null);
  }, []);

  const handleLogout = useCallback(async () => {
    if (isLoggingOut) return; // Prevent multiple simultaneous logout attempts

    try {
      setIsLoggingOut(true);
      setError(null);
      
      // Close confirmation modal
      setShowLogoutConfirm(false);
      
      // Perform logout
      await performLogout(router);
    } catch (error) {
      console.error('❌ Error during logout:', error);
      setError(error instanceof Error ? error.message : 'An error occurred during logout');
      
      // Still try to redirect on error
      try {
        router.replace('/sign-in');
      } catch (redirectError) {
        console.error('❌ Failed to redirect after logout error:', redirectError);
        // Force redirect as last resort
        if (typeof window !== 'undefined') {
          window.location.href = '/sign-in';
        }
      }
    } finally {
      setIsLoggingOut(false);
    }
  }, [router, isLoggingOut]);

  return {
    isLoggingOut,
    showLogoutConfirm,
    handleLogoutClick,
    handleLogout,
    handleCancelLogout,
    error
  };
};
